package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// 计算当月第几周
func getWeekOfMonth(t time.Time) int {
	_, week := t.ISOWeek()
	currentMonth := t.Month()
	firstDay := time.Date(t.Year(), currentMonth, 1, 0, 0, 0, 0, t.Location())
	_, firstWeek := firstDay.ISOWeek()
	return week - firstWeek + 1
}

// 生成目标文件夹名
func generateFolderName(t time.Time) string {
	weekNum := getWeekOfMonth(t)
	return fmt.Sprintf("%d%02dW%d", t.Year(), t.Month(), weekNum)
}

// 使用winscp下载最新文件并更新到服务器上
func main() {
	// 服务器IP地址配置
	serverIP := "************"
	remote_enable := true

	if remote_enable {
		// 远程到服务器，停止服务
		fmt.Println("Stopping remote service...")

		// 使用单行命令执行远程服务停止
		cmd := exec.Command("winscp.com", "/command",
			fmt.Sprintf("open sftp://root:Password@_@%s", serverIP),
			"call systemctl stop fist",
			"exit")

		if output, err := cmd.CombinedOutput(); err != nil {
			fmt.Printf("Failed to execute command: %v\nOutput: %s\n", err, output)
			return
		}

		fmt.Println("Remote service stopped.")
	}

	// 查找ftp目录下所有文件并找出最新的
	fmt.Println("Searching for latest file...")

	cmd := exec.Command("winscp.com", "/command",
		"open *****************************************",
		"cd /rd-it-temp-out/xKF7490/developmentVersion/",
		"ls",
		"exit")

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Failed to list files: %v\nOutput: %s\n", err, output)
		return
	}

	// 解析输出找到最新文件
	files := strings.Split(string(output), "\n")
	var latestFile string
	var latestTime time.Time

	re := regexp.MustCompile(`plugins(\d{14})\.zip`)

	for _, file := range files {
		if matches := re.FindStringSubmatch(file); len(matches) > 1 {
			timeStr := matches[1]
			fileTime, err := time.ParseInLocation("20060102150405", timeStr, time.Local)
			if err != nil {
				continue
			}

			if latestTime.IsZero() || fileTime.After(latestTime) {
				latestTime = fileTime
				latestFile = matches[0]
			}
		}
	}

	if latestFile != "" {
		fmt.Printf("Found latest file: %s\n", latestFile)

		// 创建目标文件夹
		now := time.Now()
		folderName := generateFolderName(now)
		targetDir := filepath.Join(`C:\Users\<USER>\workspace`, folderName)

		err := os.MkdirAll(targetDir, 0755)
		if err != nil {
			fmt.Printf("Failed to create directory: %v\n", err)
			return
		}

		fmt.Printf("Downloading to folder: %s\n", targetDir)

		// 下载文件
		cmd := exec.Command("winscp.com", "/command",
			"open *****************************************",
			"cd /rd-it-temp-out/xKF7490/developmentVersion/",
			fmt.Sprintf("get %s %s\\%s", latestFile, targetDir, latestFile),
			"exit")

		if output, err := cmd.CombinedOutput(); err != nil {
			fmt.Printf("Failed to download file: %v\nOutput: %s\n", err, output)
			return
		}

		fmt.Printf("Successfully downloaded %s to %s\n", latestFile, targetDir)

		// 解压文件到下载目录
		fmt.Println("Extracting file...")

		// 构建源文件完整路径
		zipPath := filepath.Join(targetDir, latestFile)

		// 使用7zip解压文件，-y表示对所有询问自动回答yes（覆盖）
		cmd = exec.Command("7z", "x", zipPath, "-o"+targetDir, "-y")

		if output, err := cmd.CombinedOutput(); err != nil {
			fmt.Printf("Failed to extract file: %v\nOutput: %s\n", err, output)
			return
		}

		fmt.Printf("Successfully extracted %s to %s\n", latestFile, targetDir)

	} else {
		fmt.Println("No matching files found")
		return
	}

	if remote_enable {
		// 上传解压后的plugins文件夹到服务器，并开启服务
		fmt.Println("Uploading plugins folder to remote server and starting service...")

		// 构建plugins文件夹路径
		now := time.Now()
		folderName := generateFolderName(now)
		targetDir := filepath.Join(`C:\Users\<USER>\workspace`, folderName)
		pluginsPath := filepath.Join(targetDir, "plugins")

		// 检查plugins文件夹是否存在
		if _, err := os.Stat(pluginsPath); os.IsNotExist(err) {
			fmt.Printf("Plugins folder not found at: %s\n", pluginsPath)
			return
		}
		// 执行上传命令
		uploadCommand := []string{
			"/command",
			fmt.Sprintf("open sftp://root:Password@_@%s/usr/local/appfist/AppFist_now/", serverIP),
			fmt.Sprintf("put %s /usr/local/appfist/AppFist_now/", pluginsPath),
			"/exit"}
		cmd := exec.Command("winscp.com ", uploadCommand...)
		if output, err := cmd.CombinedOutput(); err != nil {
			fmt.Printf("Failed to upload plugins folder: %v\nOutput: %s\n", err, output)
			return
		}
		fmt.Printf("Successfully uploaded plugins folder to %s\n", "/usr/local/appfist/AppFist_now/")

		// 执行启动服务命令
		startCommand := []string{"/command",
			"open sftp://root:Password@_@************",
			"call systemctl restart fist",
			"exit"}

		cmd = exec.Command("winscp.com", startCommand...)
		if output, err := cmd.CombinedOutput(); err != nil {
			fmt.Printf("Failed to start service: %v\nOutput: %s\n", err, output)
			return
		}
		fmt.Println("Successfully restarted service")
	}

}
